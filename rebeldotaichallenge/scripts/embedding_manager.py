#!/usr/bin/env python3
"""
Embedding Management Scripts

This module provides command-line scripts for managing embeddings in the PGVector database.
It includes functionality for creating, updating, and managing collections efficiently.
"""

import argparse
import json
import logging
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

from rebeldotaichallenge.database.pg_vector import (
    EmbeddingBatch,
    LangChainPGVectorStore,
    UpdateResult,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class EmbeddingManager:
    """Manager class for embedding operations."""

    def __init__(self, connection_string: str):
        """Initialize the embedding manager."""
        self.connection_string = connection_string
        self.store = LangChainPGVectorStore(
            connection_string=connection_string,
            collection_name="default",
        )

    def create_embeddings_from_file(
        self,
        file_path: str,
        collection_name: str = "default",
        batch_size: int = 100,
        skip_existing: bool = True,
    ) -> UpdateResult:
        """
        Create embeddings from a JSON file.

        Expected file format:
        [
            {
                "text": "content to embed",
                "metadata": {"key": "value"},
                "id": "optional_id"
            },
            ...
        ]
        """
        logger.info(f"Loading data from {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"Error loading file {file_path}: {e}")
            raise

        texts = []
        metadatas = []
        ids = []

        for item in data:
            if isinstance(item, dict):
                texts.append(item.get("text", ""))
                metadatas.append(item.get("metadata", {}))
                ids.append(item.get("id"))
            elif isinstance(item, str):
                texts.append(item)
                metadatas.append({})
                ids.append(None)

        # Filter out None IDs
        if any(id is None for id in ids):
            ids = None

        batch = EmbeddingBatch(
            texts=texts,
            metadatas=metadatas,
            ids=ids,
            collection_name=collection_name,
            batch_size=batch_size,
        )

        logger.info(f"Creating embeddings for {len(texts)} texts in collection '{collection_name}'")
        return self.store.create_embeddings_batch(batch, skip_existing=skip_existing)

    def update_embeddings_from_file(
        self,
        file_path: str,
        collection_name: str = "default",
        batch_size: int = 100,
        update_existing: bool = True,
    ) -> UpdateResult:
        """Update embeddings from a JSON file."""
        logger.info(f"Updating embeddings from {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            logger.error(f"Error loading file {file_path}: {e}")
            raise

        texts = []
        metadatas = []
        ids = []

        for item in data:
            if isinstance(item, dict):
                texts.append(item.get("text", ""))
                metadatas.append(item.get("metadata", {}))
                ids.append(item.get("id"))
            elif isinstance(item, str):
                texts.append(item)
                metadatas.append({})
                ids.append(None)

        # Filter out None IDs
        if any(id is None for id in ids):
            ids = None

        logger.info(f"Updating embeddings for {len(texts)} texts in collection '{collection_name}'")
        return self.store.update_embeddings(
            texts=texts,
            metadatas=metadatas,
            ids=ids,
            collection_name=collection_name,
            update_existing=update_existing,
            batch_size=batch_size,
        )

    def create_collection(
        self,
        collection_name: str,
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Create a new collection."""
        logger.info(f"Creating collection '{collection_name}'")
        return self.store.create_collection(
            collection_name=collection_name,
            description=description,
            metadata=metadata,
        )

    def list_collections(self):
        """List all collections."""
        collections = self.store.list_collections()
        logger.info(f"Found {len(collections)} collections")
        return collections

    def get_collection_stats(self, collection_name: str):
        """Get statistics for a collection."""
        return self.store.get_collection_stats(collection_name)

    def delete_collection(self, collection_name: str):
        """Delete a collection."""
        logger.warning(f"Deleting collection '{collection_name}' and all its documents")
        return self.store.delete_collection(collection_name)


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(description="Embedding Management Tool")
    parser.add_argument(
        "--connection-string",
        required=True,
        help="PostgreSQL connection string",
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Create embeddings command
    create_parser = subparsers.add_parser("create", help="Create embeddings from file")
    create_parser.add_argument("--file", required=True, help="JSON file with texts to embed")
    create_parser.add_argument("--collection", default="default", help="Collection name")
    create_parser.add_argument("--batch-size", type=int, default=100, help="Batch size")
    create_parser.add_argument("--no-skip-existing", action="store_true", help="Don't skip existing documents")

    # Update embeddings command
    update_parser = subparsers.add_parser("update", help="Update embeddings from file")
    update_parser.add_argument("--file", required=True, help="JSON file with texts to update")
    update_parser.add_argument("--collection", default="default", help="Collection name")
    update_parser.add_argument("--batch-size", type=int, default=100, help="Batch size")
    update_parser.add_argument("--no-update-existing", action="store_true", help="Don't update existing documents")

    # Collection management commands
    collection_parser = subparsers.add_parser("collection", help="Collection management")
    collection_subparsers = collection_parser.add_subparsers(dest="collection_command")
    
    # Create collection
    create_coll_parser = collection_subparsers.add_parser("create", help="Create collection")
    create_coll_parser.add_argument("name", help="Collection name")
    create_coll_parser.add_argument("--description", help="Collection description")
    create_coll_parser.add_argument("--metadata", help="Collection metadata (JSON)")

    # List collections
    collection_subparsers.add_parser("list", help="List collections")

    # Collection stats
    stats_parser = collection_subparsers.add_parser("stats", help="Get collection statistics")
    stats_parser.add_argument("name", help="Collection name")

    # Delete collection
    delete_parser = collection_subparsers.add_parser("delete", help="Delete collection")
    delete_parser.add_argument("name", help="Collection name")
    delete_parser.add_argument("--confirm", action="store_true", help="Confirm deletion")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    manager = EmbeddingManager(args.connection_string)

    try:
        if args.command == "create":
            result = manager.create_embeddings_from_file(
                file_path=args.file,
                collection_name=args.collection,
                batch_size=args.batch_size,
                skip_existing=not args.no_skip_existing,
            )
            print(f"Created: {result.new_count}, Skipped: {result.skipped_count}")
            if result.errors:
                print(f"Errors: {result.errors}")

        elif args.command == "update":
            result = manager.update_embeddings_from_file(
                file_path=args.file,
                collection_name=args.collection,
                batch_size=args.batch_size,
                update_existing=not args.no_update_existing,
            )
            print(f"Updated: {result.updated_count}, New: {result.new_count}, Skipped: {result.skipped_count}")
            if result.errors:
                print(f"Errors: {result.errors}")

        elif args.command == "collection":
            if args.collection_command == "create":
                metadata = json.loads(args.metadata) if args.metadata else None
                collection_info = manager.create_collection(
                    collection_name=args.name,
                    description=args.description,
                    metadata=metadata,
                )
                print(f"Created collection: {collection_info.name}")

            elif args.collection_command == "list":
                collections = manager.list_collections()
                for coll in collections:
                    print(f"- {coll['name']}: {coll.get('metadata', {})}")

            elif args.collection_command == "stats":
                stats = manager.get_collection_stats(args.name)
                print(f"Collection: {stats['name']}")
                print(f"Documents: {stats['document_count']}")
                print(f"Embedding dimension: {stats['embedding_dimension']}")

            elif args.collection_command == "delete":
                if not args.confirm:
                    print("Use --confirm to delete the collection")
                    return
                success = manager.delete_collection(args.name)
                if success:
                    print(f"Deleted collection: {args.name}")
                else:
                    print(f"Failed to delete collection: {args.name}")

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

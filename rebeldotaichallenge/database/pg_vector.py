import asyncio
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

# Lang<PERSON>hain imports
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from langchain_postgres import PGVector
from pydantic import BaseModel, Field, SecretStr

from rebeldotaichallenge._params import OPENAI_API_KEY, OPENAI_EMBEDDING_MODEL_ID

logger = logging.getLogger(__name__)


class EmbeddingDocument(BaseModel):
    """Model for embedding documents"""

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embedding: Optional[List[float]] = None
    collection_name: str = "default"


class CollectionInfo(BaseModel):
    """Model for collection information"""

    id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: Optional[str] = None
    embedding_model: str = "text-embedding-ada-002"
    embedding_dimension: int = 1536
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SearchResult(BaseModel):
    """Model for search results"""

    document: EmbeddingDocument
    similarity_score: float
    rank: int


class LangChainPGVectorStore:
    """
    LangChain-compatible wrapper for PGVector with enhanced functionality.

    This class provides a bridge between the custom PGVectorStore implementation
    and LangChain's VectorStore interface, offering the best of both worlds.
    """

    def __init__(
        self,
        connection_string: str,
        collection_name: str = "default",
        pre_delete_collection: bool = False,
    ):
        """
        Initialize LangChain-compatible PGVector store.

        Args:
            connection_string: PostgreSQL connection string
            collection_name: Name of the collection
            pre_delete_collection: Whether to delete existing collection
        """
        # Use OpenAI embeddings as default
        if not OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is not set")

        self.embeddings = OpenAIEmbeddings(
            model=OPENAI_EMBEDDING_MODEL_ID,
            api_key=SecretStr(OPENAI_API_KEY),
        )

        # Initialize LangChain PGVector
        self.langchain_store = PGVector(
            connection=connection_string,
            embeddings=self.embeddings,
            collection_name=collection_name,
            pre_delete_collection=pre_delete_collection,
        )

    def add_texts(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None,
        **kwargs: Any,
    ) -> List[str]:
        """
        Add texts to the vector store using LangChain interface.

        Args:
            texts: List of texts to add
            metadatas: Optional list of metadata dicts
            ids: Optional list of document IDs
            **kwargs: Additional arguments

        Returns:
            List of document IDs
        """
        return self.langchain_store.add_texts(
            texts=texts, metadatas=metadatas, ids=ids, **kwargs
        )

    def add_documents(self, documents: List[Document], **kwargs: Any) -> List[str]:
        """
        Add LangChain documents to the vector store.

        Args:
            documents: List of LangChain Document objects
            **kwargs: Additional arguments

        Returns:
            List of document IDs
        """
        return self.langchain_store.add_documents(documents, **kwargs)

    def similarity_search_with_score(
        self,
        query: str,
        k: int = 4,
        filter: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> List[Tuple[Document, float]]:
        """
        Perform similarity search with scores using LangChain interface.

        Args:
            query: Query string
            k: Number of results to return
            filter: Optional metadata filter
            **kwargs: Additional arguments

        Returns:
            List of (document, score) tuples
        """
        return self.langchain_store.similarity_search_with_score(
            query=query, k=k, filter=filter, **kwargs
        )


if __name__ == "__main__":
    db = LangChainPGVectorStore(
        connection_string="postgresql+psycopg://langchain:langchain@localhost:6024/langchain",
        collection_name="test",
    )
    db.add_texts(["2", "6"])
    print(db.similarity_search_with_score("4", k=2))
